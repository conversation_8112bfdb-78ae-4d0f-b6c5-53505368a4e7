<script setup lang="ts">
import { onMounted } from 'vue'
import { JyType } from '@/api/transactionLog/indexType'
import { useI18n } from 'vue-i18n'
import { changeMoney } from '@/common/common'
import { useCounterStore } from '@/store/store'
const store = useCounterStore()

const { t } = useI18n()
interface PropsType {
  data: Array<JyType>
}

const props = defineProps<PropsType>()

onMounted(() => {
  console.log(props.data, 'props')
})
const emit = defineEmits(['bottom'])
const bottomFn = () => {
  emit('bottom')
}
</script>
<template>
  <scroll-view scroll-y :style="{ height: `calc(${store.pageHeight} - 3.125rem)` }" @scrolltolower="bottomFn">
    <NotData v-if="data.length === 0" />
    <view class="list-box">
      <view v-for="item in props.data" :key="item.id" class="item">
        <div class="head">{{ item.name }}</div>
        <div class="title">{{ item.gpName }}</div>
        <div class="label">{{ item.gpCode }}</div>
        <div class="label">{{ t('transactionLog.tip1') }}：{{ item.order_sn }}</div>
        <div class="row">
          <div class="label">{{ item.createtime }}</div>
          <div class="price" :class="item.money > 0 ? 'txt-green' : 'txt-red'">{{ changeMoney(item.money) }}</div>
        </div>
      </view>
    </view>
  </scroll-view>
</template>
<style lang="scss" scoped>
.txt-green {
  color: #0cd17c;
}

.txt-red {
  color: #ff0021;
}

.mt-10 {
  margin-top: 0.625rem;
}

.record-text {
  margin-top: 1.25rem;
  font-size: 0.75rem;
  color: #999;
}
.list-box .item:first-child {
  margin-top: 0.625rem;
}
.list-box {
  padding-bottom: 1.25rem;
  padding: 0 0.75rem 1rem;
  .item {
    width: 100%;
    padding: 1.3125rem 0.9375rem 0 0.9375rem;
    box-sizing: border-box;
    border-radius: 0.375rem;
    margin-top: 0.625rem;

    background: $color-white;
    border-radius: 0.63rem;
    padding: 0.63rem;
    margin: 0.69rem 0;
    border: 0.1rem solid #f3f3f2;
    .head {
      font-size: 0.94rem;
      font-weight: 500;
      text-align: center;
      color: $color-black;
      position: relative;
      margin-bottom: 1.13rem;
      &::before {
        content: '';
        width: 4.38rem;
        height: 0.1rem;
        background: $color-primary;
        border-radius: 0.19rem;
        bottom: -0.16rem;
        position: absolute;
        left: calc(50% - 2.19rem);
      }
    }
    .title {
      color: $color-black;
      font-size: 0.94rem;
      font-weight: 500;
    }
    .label {
      font-size: 0.81rem;
      color: $color-gray;
    }
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .price {
        font-size: 1.44rem;
        font-weight: 500;
      }
    }
    .info-box {
      height: 4.5938rem;
      background: rgb(13, 46, 85, 0.7);
      border-radius: 0.9375rem;
      margin-top: 0.625rem;
      padding: 1rem 0.625rem 0.9063rem 0.9375rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .money {
        font-size: 1rem;
        font-weight: 600;
      }

      .info-box-left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .gpName {
          color: #fff;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .biaohao {
          color: #a0a5b0;
          font-size: 0.8125rem;
        }
      }
    }
    .time {
      font-size: 0.8125rem;
      color: #a0a5b0;
      line-height: 0.8125rem;
      text-align: right;
      font-weight: 400;
    }

    .date {
      display: flex;
      align-items: center;
      font-size: 0.9375rem;
      max-height: 14.5625rem;
      color: #545861;
      margin-top: 0.3125rem;

      .gpCode {
        color: #0f75fc;
        font-size: 0.75rem;
        font-weight: 500;
        background: #ddebff;
        border-radius: 0.1875rem;
        line-height: 1.0625rem;
        padding: 0.125rem 0.375rem;
        margin-right: 0.3125rem;
      }
      .name {
        font-size: 0.9375rem;
        color: #fff;
        font-weight: 500;
      }
    }

    .note {
      font-weight: 600;
      // color: #cacee0;
    }

    .xian {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  .list-item {
    width: 21.5625rem;
    height: 5.1875rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 0.5313rem 1.375rem 0.7188rem 1.375rem;
    background: #ffffff;
    border-radius: 0.9375rem;
    margin-bottom: 0.4375rem;

    .list-item-bottom {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .list-item-bottom-right {
        font-weight: 300;
        font-size: $uni-font-size-1;
        color: #1b70ab;
      }

      .list-item-bottom-left {
        display: flex;
        align-items: center;
        width: 11.875rem;
        justify-content: space-between;

        .list-item-bottom-remarks {
          font-weight: 300;
          font-size: $uni-font-size-1;
          color: #1b70ab;
          display: flex;
          justify-content: center;
          align-items: center;

          span {
            color: #c02d1a;
            margin-right: 0.1563rem;
          }
        }

        .list-item-bottom-money {
          font-weight: 300;
          font-size: $uni-font-size-1;
          color: #1b70ab;
          // margin-right: 5.3125rem;
        }
      }
    }

    .list-item-top {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .list-item-top-rigth {
        font-weight: 300;
        font-size: $uni-font-size-1;
        color: #000203;
      }

      .list-item-top-left {
        display: flex;
        align-items: center;

        .list-item-money {
          width: 3.4688rem;

          font-weight: 300;
          font-size: $uni-font-size-1;
          color: #000203;
        }

        .list-item-tag {
          width: 5.6563rem;
          // height: 0.875rem;
          display: flex;
          justify-content: center;
          align-items: center;
          // background: #a6d5f5;
          border-radius: 0.4375rem;

          font-weight: 300;
          font-size: $uni-font-size-1;
          // color: #fdfefe;

          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: pre-wrap; //溢出不换行
        }
      }
    }
  }
}
</style>
