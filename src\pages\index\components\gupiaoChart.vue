<template>
  <view id="indexChart" style="width: 100%; height: 154.206px" :style="{ height: height }"></view>
</template>

<script lang="ts" setup>
import { init, dispose } from 'klinecharts'
import { onMounted, onUnmounted, watch } from 'vue'
const props = defineProps({
  value: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '154px'
  }
})
let chart: any = null
watch(
  () => props.value,
  (newData) => {
    chart?.applyNewData(newData)
  },
  { deep: true }
)
const chartOption = {
  candle: {
    type: 'candle_solid',
    bar: {
      upColor: '#50CD89',
      downColor: '#FF6254',
      noChangeColor: '#888888',
      upBorderColor: '#50CD89',
      downBorderColor: '#FF6254',
      noChangeBorderColor: '#888888',
      upWickColor: '#50CD89',
      downWickColor: '#FF6254',
      noChangeWickColor: '#888888'
    },
    tooltip: {
      showRule: 'none'
    }
  },
  grid: {
    show: true,
    horizontal: {
      show: false // ❌ 关闭 X 轴方向的虚线（横向）
    },
    vertical: {
      show: false, // ✅ 保留 Y 轴方向虚线
      size: 1,
      color: '#c9c9c9',
      style: 'dashed',
      dashedValue: [2, 2]
    },
    border: {
      top: false,
      bottom: false, // ❌ 去掉底部边框
      left: false,
      right: false // ❌ 去掉右侧边框
    }
  },
  xAxis: {
    tickText: {
      color: '#333'
    }
  },
  yAxis: {
    tickText: {
      color: '#333'
    }
  }
}

onMounted(() => {
  chart = init('indexChart')
  chart.setStyles(chartOption)
})

onUnmounted(() => {
  dispose('chart')
})
// const chartOption = {
//   // 网格线
//   grid: {
//     show: true,
//     horizontal: {
//       show: true,
//       size: 1,
//       color: '#333',
//       style: 'dashed',
//       dashedValue: [2, 2]
//     },
//     vertical: {
//       show: true,
//       size: 1,
//       color: '#333',
//       style: 'dashed',
//       dashedValue: [2, 2]
//     }
//   },
//   // x轴
//   xAxis: {
//     show: true,
//     size: 'auto',
//     // x轴线
//     axisLine: {
//       show: true,
//       color: '#666',
//       size: 1
//     },
//     // x轴分割文字
//     tickText: {
//       show: true,
//       color: '#D9D9D9',
//       family: 'Helvetica Neue',
//       weight: 'normal',
//       size: 12,
//       marginStart: 4,
//       marginEnd: 4
//     },
//     // x轴分割线
//     tickLine: {
//       show: true,
//       size: 1,
//       length: 3,
//       color: '#666'
//     }
//   },
//   // y轴
//   yAxis: {
//     show: true,
//     size: 'auto',
//     // 'left' | 'right'
//     position: 'right',
//     // 'normal' | 'percentage' | 'log'
//     type: 'normal',
//     inside: false,
//     reverse: false,
//     // y轴线
//     axisLine: {
//       show: true,
//       color: '#666',
//       size: 1
//     },
//     // x轴分割文字
//     tickText: {
//       show: true,
//       color: '#D9D9D9',
//       family: 'Helvetica Neue',
//       weight: 'normal',
//       size: 12,
//       marginStart: 4,
//       marginEnd: 4
//     },
//     // x轴分割线
//     tickLine: {
//       show: true,
//       size: 1,
//       length: 3,
//       color: '#666'
//     }
//   },
//   // 图表之间的分割线
//   separator: {
//     size: 1,
//     color: '#666',
//     fill: true,
//     activeBackgroundColor: 'rgba(230, 230, 230, .15)'
//   },
//   crosshair: {
//     show: true,
//     // 十字光标水平线及文字
//     horizontal: {
//       show: true,
//       line: {
//         show: true,
//         style: 'solid',
//         size: 1,
//         color: '#333'
//       }
//     },
//     vertical: {
//       show: true,
//       line: {
//         show: true,
//         style: 'solid',
//         size: 1,
//         color: '#333'
//       }
//     }
//   }
// }
</script>
<style scoped lang="scss"></style>
