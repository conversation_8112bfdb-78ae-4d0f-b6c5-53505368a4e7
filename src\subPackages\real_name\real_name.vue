<script setup lang="ts">
import { computed, Ref, ref } from 'vue'
import Navigator from '@/components/navigator/navigator.vue'
import { getRealnameApi, subRealnameApi } from '@/api/real_name'
import { RealNameType } from '@/api/real_name/indexType'
import { useI18n } from 'vue-i18n'
import { onLoad } from '@dcloudio/uni-app'
import { closeToast, showLoadingToast, showSuccessToast, showToast } from 'vant'
import { useCounterStore } from '@/store/store'
import config from '@/utils/config'
const store = useCounterStore()
const { t } = useI18n()
const form: Ref<RealNameType> = ref({
  true_name: '',
  id_card: '',
  id_card_img_1: '',
  id_card_img_2: '',
  status_msg: null,
  status: ''
})

const progressImg1 = ref(false)
const progressImgjidu1 = ref(50)
const progressImg2 = ref(false)
const progressImgjidu2 = ref(19)
const id_card_img_1 = ref('')
const id_card_img_2 = ref('')

const getRealname = async () => {
  showLoadingToast({
    message: t('toastText.tip1'),
    forbidClick: true,
    overlay: true,
    loadingType: 'spinner'
  })
  const res = await getRealnameApi()
  if (res.code === 1) {
    if (res.data) {
      form.value = res.data
      id_card_img_1.value = form.value.id_card_img_1
      id_card_img_2.value = form.value.id_card_img_2
    }
  } else {
    showToast(res.msg)
  }
  closeToast()
}

onLoad(() => {
  getRealname()
})
let istrue = true
// 提交实名
const submitRealName = async () => {
  if (store.userInfo?.is_auth === 1 && form.value.status !== '') {
    return showToast(t('real_name.tip1'))
  }
  if (form.value.status === '0') {
    return showToast(t('real_name.tip2'))
  }
  if (!istrue) return
  istrue = false
  const res = await subRealnameApi(form.value)
  setTimeout(() => {
    istrue = true
  }, 1000)
  if (res.code === 1) {
    showSuccessToast(res.msg)
    setTimeout(() => {
      getRealname()
      store.getUserInfo()
    }, 2000)
  } else {
    showToast(res.msg)
  }
}

const baseURL = computed(() => {
  return config.baseURL
})

const uploadImage = (type: number) => {
  if (form.value.status === '0') {
    return showToast(t('real_name.tip2'))
  }
  if (form.value.status === '1') {
    return showToast(t('real_name.tip3'))
  }
  try {
    uni.chooseImage({
      sourceType: ['album', 'camera'], // 从相册选择
      sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
      success: (chooseImageRes) => {
        const tempFilePaths = chooseImageRes.tempFilePaths
        const filePath = tempFilePaths[0]

        console.log(tempFilePaths, 'tempFilePaths')
        // showToast(filePath)

        switch (type) {
          case 1:
            id_card_img_1.value = filePath
            progressImg1.value = true
            progressImgjidu1.value = 60
            break
          case 2:
            id_card_img_2.value = filePath
            progressImg2.value = true
            progressImgjidu2.value = 69
            break
        }
        uni.showLoading({
          title: t('real_name.tip16'),
          mask: true
        })
        uni.uploadFile({
          // url: '/api.php/common/upload', // 仅爲示例，非真实的接口地址
          url: baseURL.value + '/common/upload?lang=' + uni.getStorageSync('locale'),
          // url: 'https://adminjdhgjhs.jpinsoo.com/api.php/common/upload?lang=' + uni.getStorageSync('locale'), // 仅爲示例，非真实的接口地址
          // url: 'https://admin.pzenabackend.com/api.php/common/upload?lang=' + uni.getStorageSync('locale'), // 仅爲示例，非真实的接口地址
          filePath: filePath,
          name: 'file',
          header: {
            token: uni.getStorageSync('token')
          },
          success: (uploadFileRes) => {
            const resData = JSON.parse(uploadFileRes.data)
            if (resData.code !== 1) {
              showToast(t('real_name.tip4'))
            }
            // showToast(tempFilePaths + '---' + resData)
            console.log('resData.data.fullurl', resData.data.fullurl)
            switch (type) {
              case 1:
                form.value.id_card_img_1 = resData.data.fullurl
                // id_card_img_1.value = resData.data.fullurl
                progressImgjidu1.value = 99
                progressImg1.value = false
                showSuccessToast(t('real_name.tip5'))
                break
              case 2:
                form.value.id_card_img_2 = resData.data.fullurl
                // id_card_img_2.value = resData.data.fullurl
                progressImgjidu2.value = 99
                progressImg2.value = false
                showSuccessToast(t('real_name.tip6'))
                break
            }
            uni.hideLoading()
          }
        })
      }
    })
  } catch (error) {
    showToast(error + 'xxx')
  }
}
</script>
<template>
  <Navigator :title="t('real_name.realName')" />
  <view v-if="form.status == '0'">
    <view class="u-text-center u-font-xl" style="color: #333; font-weight: 500">{{ t('real_name.tip2') }}</view>
  </view>
  <view v-if="form.status == '1'">
    <view class="u-text-center u-font-xl" style="color: #333; font-weight: 500">{{ t('real_name.tip7') }}</view>
  </view>
  <view v-if="form.status == 2" style="height: 3.13rem; width: 100%; color: #333; display: flex; align-items: center; padding: 15rpx; justify-content: center">
    {{ t('real_name.tip15') }}{{ form.status_msg }}
  </view>
  <view class="info-list">
    <view class="input-item" style="margin-bottom: 0.9375rem">
      <view class="input-item-lebal">{{ t('real_name.tip8') }}</view>
      <input v-model="form.true_name" type="text" :disabled="form.status === '1' || form.status == '0'" :placeholder="t('real_name.tip9')" style="text-align: right" />
    </view>
    <view class="input-item">
      <view class="input-item-lebal">{{ t('real_name.tip10') }}</view>
      <input v-model="form.id_card" :disabled="form.status === '1' || form.status == '0'" type="text" maxlength="16" :placeholder="t('real_name.tip11')" style="text-align: right" />
    </view>
  </view>
  <view class="upload-image">
    <view class="upload-image-box" @click="uploadImage(1)">
      <image :src="id_card_img_1 != '' ? id_card_img_1 : `/static/image/authentication/zheng1.png`" mode=""> </image>
      <view style="font-size: 0.5rem; color: #333; font-weight: 400; text-align: center">{{ t('real_name.tip12') }}</view>
      <v-solt>
        <u-line-progress v-show="progressImg1" active-color="#2979ff" :striped="true" :striped-active="true" :percent="progressImgjidu1"></u-line-progress>
      </v-solt>
    </view>
    <view @click="uploadImage(2)">
      <image class="upload-image-box" :src="id_card_img_2 != '' ? id_card_img_2 : `/static/image/authentication/fan1.png`" mode=""> </image>
      <view style="font-size: 0.5rem; color: #333; font-weight: 400; text-align: center">{{ t('real_name.tip13') }}</view>
      <v-slot>
        <u-line-progress v-show="progressImg2" active-color="#2979ff" :striped="true" :striped-active="true" :percent="progressImgjidu2"></u-line-progress>
      </v-slot>
    </view>
  </view>

  <butto v-if="form.status === '2' || form.status === ''" :disabled="progressImg1 == true || progressImg2 == true || form.status === '1'" class="submit" @click="submitRealName">
    {{ t('real_name.tip14') }}
  </butto>
</template>
<style lang="scss" scoped>
::v-deep .uni-toast__content {
}
::v-deep .uni-toast {
  padding: 0rem 0.63rem;
}
input {
  font-size: 1.125rem;
  height: 1.875rem;
  line-height: 0.9375rem;
}

.wrap {
  box-sizing: border-box;
}

.u-text-center {
  margin-top: 2.0313rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.input-item {
  padding: 0 1.25rem;
  height: 3.375rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 0.05rem solid #f2f4f6;
  ::v-deep .uni-input-placeholder {
    font-weight: 500;
    color: $color-gray;
    font-size: 0.9375rem;
  }
  .input-item-lebal {
    font-size: 0.8125rem;
    color: $color-gray;
  }
  input {
    font-weight: 500;
    font-size: 0.9375rem;
    color: #c0c2c4;
    padding-left: 1.5rem;
    flex: 1;
  }
}

.upload-image {
  display: flex;
  justify-content: space-between;
  padding: 0.625rem;
  gap: 0.63rem;

  image {
    width: 100%;
    height: 6.1875rem;
    border-radius: 0.4063rem;
  }

  text {
    padding-top: 12rpx;
  }
}

.submit {
  display: block;
  height: 2.8125rem;
  line-height: 2.8125rem;
  text-align: center;
  font-size: 1rem;
  color: #fff;
  font-weight: 500;
  background: $color-primary;
  border-radius: 0.5rem;
  margin: 2.5rem 0.625rem 0.625rem 0.625rem;
}

.success-icon {
  width: 5rem;
  height: 6.25rem;
  margin-top: 10%;
}

.info-list {
  margin: 1.25rem 0.5625rem 0;
  border-radius: 0.25rem;
}
</style>
