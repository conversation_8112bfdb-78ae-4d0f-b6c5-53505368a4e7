<script setup lang="ts">
import Navigator from '@/components/navigator/navigator.vue'
import { useCounterStore } from '@/store/store'
import { getBankListApi } from '@/api/bankAccount'
import { submitWithdrawalApi } from '@/api/withdrawal'
import { BankInfoType } from '@/api/bankAccount/indexType'
import { WithdrawalType } from '@/api/withdrawal/indexType'
import { Ref, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { onShow } from '@dcloudio/uni-app'
import { closeToast, showLoadingToast, showSuccessToast, showToast } from 'vant'
import ka from '@/static/image/user/ka.png'
const { t } = useI18n()
const store = useCounterStore()

onShow(() => {
  getBankListFn()
})

const bankData: Ref<BankInfoType> = ref({
  id: null,
  bank_address: '',
  bank_card: '',
  bank_code: '',
  bank_name: '',
  shiming_name: '',
  bank_num: ''
})
type BankInfoListType = [BankInfoType]

const bankDataList: Ref<BankInfoListType> = ref([])

const checkBank = (item: BankInfoType) => {
  bankData.value = item
  showCenter.value = false
}

// 获取银行卡数据
const getBankListFn = async () => {
  showLoadingToast({
    message: t('toastText.tip1'),
    forbidClick: true,
    overlay: true,
    loadingType: 'spinner'
  })
  const res = await getBankListApi()
  if (res.code === 1) {
    bankDataList.value = res.data.user_bankcard
    if (res.data.user_bankcard.length) {
      bankData.value = res.data.user_bankcard[0]
    }
  }
  closeToast()
}
const bankNum = () => {
  const phoneNumber = bankData.value.bank_num
  if (phoneNumber) return phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(phoneNumber.length - 3)
}

const showCenter = ref(false)

const from: Ref<WithdrawalType> = ref({
  bankcard_id: '',
  money: '',
  pass: '',
  user_id: store.userId
})

const goPage = (url: string) => {
  uni.navigateTo({ url })
}

const sumbitLoading = ref(false)

const sumbit = async () => {
  from.value.bankcard_id = bankData.value?.id || null
  console.log(from.value.bankcard_id, 'from.value.bankcard_id')

  if (from.value.bankcard_id === '' || !from.value.bankcard_id) {
    return showToast(t('withdrawal.tip4'))
  }
  if (from.value.money === '' || !from.value.money) {
    return showToast(t('withdrawal.tip5'))
  }
  if (from.value.pass === '' || !from.value.pass) {
    return showToast(t('withdrawal.tip6'))
  }

  sumbitLoading.value = true

  const res = await submitWithdrawalApi(from.value)
  from.value = {
    bankcard_id: '',
    money: '',
    pass: '',
    user_id: store.userId
  }
  sumbitLoading.value = false
  if (res.code === 1) {
    showSuccessToast(res.data.msg)
    store.getUserInfo()
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/user/user'
      })
    }, 2000)
  }

  console.log(res)
}
</script>

<template>
  <van-popup v-model:show="showCenter" round class="check-bank-popup">
    <view class="check-bank-popup-title">
      {{ t('withdrawal.title') }}
    </view>
    <view v-for="item in bankDataList" :key="item.id" class="twBank" @click="checkBank(item)">
      <view class="u-flex">
        <view class="">
          <view class="u-font-blod">{{ item.bank_num }}</view>
          <view class="u-light-color">{{ item.bank_name }}&nbsp;{{ item.shiming_name }} </view>
        </view>
      </view>
      <view class="iiiimg">
        <van-icon name="arrow" color="#fff" size="1.4rem"></van-icon>
      </view>
    </view>
  </van-popup>
  <Navigator :title="t('user.tixian')" />
  <div class="container">
    <view class="top-money">
      <span>{{ t('withdrawal.zhanghuyue') }}</span> {{ Number(store.$state.userInfo?.money).toLocaleString() }}
    </view>
    <view class="box">
      <view v-if="bankDataList.length === 0" class="bank-crad" @click="goPage('/subPackages/bankAccount/bankAccount')">
        <view style="display: flex; align-items: center">
          <img :src="ka" alt="" style="width: 1.3125rem; height: 1.0625rem; margin-right: 0.625rem" />
          <view class="bank-crad-left">{{ t('withdrawal.tip3') }}</view>
        </view>
        <view class="bank-crad-right">
          <van-icon name="arrow" color="#333" size="1.4rem"></van-icon>
        </view>
      </view>
      <view v-else class="bank-check-box" @click="showCenter = true">
        <view class="bank-check-box-left">
          <view class="bank-check-box-left-top">
            {{ bankData.bank_name }}&nbsp;<span>({{ bankNum() }})</span>
          </view>
          <view class="bank-check-box-left-bottom">{{ t('withdrawal.chikaren') }}:&nbsp;{{ bankData.shiming_name }}</view>
        </view>
        <view class="bank-check-box-right">
          <van-icon name="arrow" color="#fff" size="1.4rem"></van-icon>
        </view>
      </view>

      <view class="recharge-box">
        <view class="recharge-box-title">{{ t('withdrawal.tilingjine') }}</view>
        <!-- 允许输入数字，调起带符号的纯数字键盘 -->
        <van-field v-model="from.money" type="number" :placeholder="t('withdrawal.tip1')" />
        <view class="recharge-box-title a">{{ t('withdrawal.jiaoyimima') }}</view>
        <!-- 允许输入数字，调起带符号的纯数字键盘 -->
        <van-field v-model="from.pass" type="password" :placeholder="t('withdrawal.tip2')" />

        <van-button :loading="sumbitLoading" class="sumbit" @click="sumbit">{{ t('recharge.queren') }}</van-button>
      </view>
      <view class="tip">{{ t('withdrawal.zhuyishixiang') }}:</view>
      <view class="tip1">{{ t('withdrawal.tip7') }}</view>
      <view class="tip1">{{ t('withdrawal.tip8') }}</view>
      <view class="tip1">{{ t('withdrawal.tip9') }}</view>
      <view class="tip1">{{ t('withdrawal.tip10') }}</view>
    </view>
  </div>
</template>
<style lang="scss" scoped>
.container {
  height: calc(var(--vh) * 100 - 3.13rem);
  overflow: auto;
}
.bank-crad {
  width: 100%;
  height: 4.5313rem;
  padding: 1.3438rem 2.2188rem 1.1563rem 1.2188rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .bank-crad-left {
    text-align: center;
  }
  .bank-crad-right {
    img {
      height: 1.1563rem;
    }
  }
}

.text {
  font-weight: 300;
  font-size: 0.75rem;
  color: #292828;
  margin-left: 1.125rem;
}

.check-bank-popup {
  width: 21.875rem;
  padding: 1.25rem 1.25rem 3.125rem 1.25rem;

  .twBank {
    padding: 0.9375rem 0.3125rem;
    border-radius: 0.4375rem;
    margin-top: 0.625rem;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4.63rem;
    padding: 0.94rem 0.63rem;
    background: url('@/static/image/user/bank_bg2.png');
    background-size: 100% 100%;
    border-radius: 0.63rem;
    .u-flex {
      display: flex;
      flex-direction: row;
      align-items: center;

      font-size: 0.875rem;
      .u-font-blod {
        font-weight: 600;
        color: #fff;
      }
      .u-light-color {
        color: #fff;
      }
    }
    .iiiimg {
      img {
        width: 0.75rem;
      }
    }
  }
  .check-bank-popup-title {
    font-size: 1.125rem;
    text-align: center;
  }
}

.tip {
  margin-top: 0.7813rem;
  margin-left: 1.125rem;

  font-size: $uni-font-size-1;
  color: #a0a5b0;
}

.tip1 {
  font-size: $uni-font-size-1;
  margin-left: 1.125rem;
  color: #a0a5b0;
}

.bank-check-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 4.5313rem;
  padding: 1.3438rem 0.9063rem 1.1563rem 1.2188rem;
  background: url('@/static/image/user/bank_bg2.png');
  background-size: 100% 100%;
  border-radius: 0.63rem;
  height: 5.63rem;
  width: calc(100vw - 1.88rem);
  margin: 0 0.94rem;
  padding: 0.94rem 0.63rem;
  color: #fff;

  .bank-check-box-right {
    img {
      height: 1.1563rem;
    }
  }

  .bank-check-box-left {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.63rem;

    .bank-check-box-left-top {
      font-weight: 0.125rem;
    }

    .bank-check-box-left-bottom {
      font-weight: 300;
      color: #fff;
    }
  }
}

.bank-check-box-title {
  margin-top: 0.7813rem;
  margin-left: 1.1875rem;

  font-weight: 300;
  font-size: 0.8438rem;
  color: #080707;
}

.top-money {
  height: 8.13rem;
  background: linear-gradient(94deg, #12719f 0.49%, #2093cb 99.51%);
  margin: 0 1rem;
  border-radius: 1.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  font-size: 1.6rem;
  color: #fff;
  span {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 0.94rem;
  }
}

.box {
  margin-top: 1.25rem;
  padding-bottom: 1rem;
}

.recharge-box {
  width: 100%;
  margin-top: 0.4375rem;
  padding: 1.2813rem 1.2188rem 1.0938rem 1.1563rem;

  .sumbit {
    width: 100%;
    height: 3.06rem;
    margin-top: 1.7188rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background: $color-primary;
    border-radius: 0.5rem;

    ::v-deep .van-button__text {
      font-weight: 500;
      font-size: 0.875rem;
      color: #fff;
    }
  }

  .van-field {
    margin-top: 0.7188rem;
    height: 3.06rem;
    background: #f4f4f4;
    border: 0.05rem solid $color-primary;
    // border: 0.05rem solid #aeaeae;
    border-radius: 0.63rem;
    display: flex;
    align-items: center;

    ::v-deep .van-field__control {
      line-height: 0;
      font-size: 0.875rem;
      &::placeholder {
        color: #afafaf;
      }
    }
  }

  .a {
    margin-top: 0.9063rem;
  }

  .recharge-box-title {
    font-weight: 500;
    font-size: 0.875rem;
    color: $color-gray;
  }
}
</style>
