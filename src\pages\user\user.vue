<template>
  <view class="box">
    <div class="head">
      <view class="top">
        <view class="user-info-left">
          <img class="headImg" src="/static/image/user/touxiang.png" alt="" />
          <view class="user-info-box">
            <view style="color: #333; font-size: 1rem; font-weight: 500">{{ desensitization(store.$state.userInfo?.mobile) }}</view>
            <view style="font-size: 0.8125rem; color: #333">{{ store.$state.userInfo?.true_name || t('index.tip1') }}</view>
            <span style="font-size: 0.8125rem; color: #333">{{ t('user.xinyongpingfen') }}：{{ store.$state.userInfo?.score }}</span>
          </view>
        </view>
        <view class="user-info-right" @click="goPage('/subPackages/seting/seting')">
          <van-icon name="setting-o" color="#333" size="1rem" />
        </view>
      </view>

      <view class="user-info">
        <div class="head">
          <view class="user-info-top">
            <view class="user-info-top-left">
              <view class="zichan-box">
                <view style="display: flex; align-items: center">
                  <span class="zichan-box-title">{{ t('user.zongzichan') }}</span>
                  <van-icon v-if="!isShowInfo" name="closed-eye" color="#fff" size="20" class="showIcon" @click="isShowInfo = !isShowInfo" />
                  <van-icon v-else name="eye-o" color="#fff" size="20" class="showIcon" @click="isShowInfo = !isShowInfo" />
                </view>
                <span v-if="!isShowInfo" class="zichan-box-money">******</span>
                <span v-else class="zichan-box-money">{{ changeMoney(store.$state.userInfo?.total || 0) }}</span>
              </view>
            </view>
          </view>
          <view class="user-info-buttom">
            <view v-for="(item, i) in userAssetsInfo" :key="i" class="user-info-item" :style="i === 0 ? { textAlign: 'left' } : i === 1 ? { textAlign: 'center' } : { textAlign: 'right' }">
              <view class="user-info-item-left">
                <view class="user-info-item-t">{{ t(item.t) }}</view>
              </view>
              <view v-if="!isShowInfo" class="user-info-item-money">******</view>
              <view v-else class="user-info-item-money" :class="item.class" :style="{ color: item.color, wordWrap: breakWord }">{{ changeMoney(item.num) }}</view>
            </view>
          </view>
        </div>
      </view>
    </div>
    <div class="bottom">
      <view class="user-info-top-right">
        <div class="button" @click="goWeb">
          <image class="recharge" :src="recharge" mode="" />
          <div class="label">{{ t('user.chongzhi') }}</div>
        </div>
        <div class="line"></div>
        <div class="button" @click="goPage('/subPackages/withdrawal/withdrawal')">
          <image :src="withdraw" mode="" />
          <div class="label">{{ t('user.tixian') }}</div>
        </div>
      </view>
    </div>

    <div class="blockList">
      <view v-for="(item, i) in pageListArr" :key="i" class="block" @click="blockClick(item)">
        <div class="img_wrap">
          <image :src="item.imgUrl" />
        </div>
        <div class="text">{{ t(item.name) }}</div>
      </view>
    </div>
  </view>
  <CustomTabbar :id="4" />
</template>

<script lang="ts" setup>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'
import { useI18n } from 'vue-i18n'
import { computed, ref } from 'vue'
import { useCounterStore } from '@/store/store'
import { onShow } from '@dcloudio/uni-app'
import { getCzhiurlApi } from '@/api/user'
import { changeMoney, getColor2 } from '@/common/common'
// import set from '@/static/image/user/set.png'
import recharge from '@/static/image/user/recharge2.png'
import withdraw from '@/static/image/user/withdraw2.png'
const store = useCounterStore()
// 基于准备好的dom，初始化echarts实例

const kefu_url = ref('')

const getCzhiurlFn = async () => {
  const res = await getCzhiurlApi()
  if (res.code === 1) {
    kefu_url.value = res.data.kefu_url
  }
}

// 手机号脱敏
const desensitization = (str: string) => {
  if (!str) return
  return str
    .split('')
    .map((item, i) => {
      if (i >= 3 && i <= 6) {
        return '*'
      }
      return item
    })
    .join('')
}

const goWeb = () => {
  window.open(kefu_url.value)
}
onShow(() => {
  getCzhiurlFn()
  store.getUserInfo()
})

const goPage = (url: string, is = false) => {
  if (is) {
    goWeb()
    return
  }
  uni.navigateTo({ url })
}

const { t } = useI18n()
const isShowInfo = ref(false)
const pageListArr = [
  {
    name: 'user.yinhangzhanghu',
    imgUrl: '/static/image/user/img1.png',
    url: '/subPackages/bankAccount/bankAccount'
  },
  {
    name: 'user.shouru',
    imgUrl: '/static/image/user/img2.png',
    url: '/subPackages/transactionLog/transactionLog'
  },
  {
    name: 'user.benrenqueren',
    imgUrl: '/static/image/user/img3.png',
    url: '/subPackages/real_name/real_name'
  },
  {
    name: 'user.rizhisuoyin',
    imgUrl: '/static/image/user/img4.png',
    url: '/subPackages/changePassword/changePassword'
  },
  {
    name: 'user.shiwuchuli',
    imgUrl: '/static/image/user/img5.png',
    url: '/subPackages/transactionPassions/transactionPassions'
  },
  {
    name: 'user.kefuzhongxin',
    imgUrl: '/static/image/user/img6.png',
    url: '/subPackages/customerService/customerService',
    kefu: true
  },
  {
    name: 'user.zhuxiao',
    imgUrl: '/static/image/user/img7.png',
    logout: true
  }
]
const blockClick = (e) => {
  if (e.logout) {
    store.clearState()
  } else if (e.kefu) {
    goWeb()
  } else {
    goPage(e.url)
  }
}

const money = computed(() => {
  return store.$state.userInfo?.money || 0
})
const count_market = computed(() => {
  return store.$state.userInfo?.shizhi || 0
})
const count_losses = computed(() => {
  return store.$state.userInfo?.my_losses?.count_losses || 0
})

const count_color = computed(() => {
  return getColor2(store.$state.userInfo?.my_losses?.count_losses || 0)
})

const userAssetsInfo = ref([
  {
    t: 'user.xianjinyue',
    num: money,
    tou: '#50AFF0',
    color: '#fff'
  },
  {
    t: 'user.beiyongzijin',
    num: count_market,
    tou: '#50F0AA',
    color: '#fff'
  },
  {
    t: 'user.fudongsunyi',
    num: count_losses,
    tou: '#C850F0',
    class: count_color
  }
])
</script>
<style scoped lang="scss">
::v-deep .van-button--normal {
  padding: 0;
}
uni-view {
  box-sizing: border-box;
}
* {
  font-weight: 500;
}
::v-deep .van-button {
  width: 4.0625rem;
  height: 1.5rem;
  border-radius: 1.5625rem;

  .van-button__text {
    font-size: $uni-font-size-1;
  }
}

.box {
  height: calc(var(--vh) * 100 - 4.375rem);
  overflow: auto;
  .page-list-box {
    margin-top: 0.75rem;

    .page-list-item {
      margin: 0 0.94rem;
      height: 3.6875rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.5rem;
      border-bottom: 0.05rem solid #ffffff50;
      background: #f8fafc;
      margin-bottom: 0.31rem;
      border-radius: 0.75rem;
      .image_wrap {
        width: 1.44rem;
        height: 1.44rem;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 1.5rem;
      }
      .page-list-item-icon {
        width: 0.94rem;
        height: 0.94rem;
      }
      .name {
        color: $color-black;
        font-size: 0.875rem;
      }
    }
  }

  .user-info {
    width: 21.5625rem;
    height: 10.63rem;
    border-radius: 1.25rem;
    margin: 0rem auto 0;
    background: linear-gradient(94deg, #12719f 0.49%, #2093cb 99.51%);
    position: relative;
    z-index: 100;
    .head {
      padding: 0.94rem;
      border-radius: 1.5rem 1.5rem 0 0;
      .user-info-buttom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.9375rem;
      }

      .user-info-top {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        margin-top: 1rem;
        .user-info-top-left {
          .zichan-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            .showIcon {
              margin-left: 0.625rem;
            }

            .zichan-box-title {
              font-size: 0.81rem;
              color: #fff;
            }

            .zichan-box-money {
              font-weight: 500;
              font-size: 1.2rem;
              color: $color-white;
              text-align: center;
            }
          }
        }
      }
      .user-info-item {
        width: 6.5625rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-top: 0.8rem;

        .user-info-item-money {
          width: 100%;
          line-height: 1rem;
          font-size: 0.85rem;
          font-weight: 500;
          color: $color-white;
          margin-top: 0.3125rem;
          word-wrap: break-word;
          white-space: nowrap;
        }

        .user-info-item-left {
          .user-info-item-t {
            font-size: 0.8rem;
            line-height: 0.875rem;
            color: #fff;
          }

          .user-info-item-tou {
            width: 0.4063rem;
            height: 0.4063rem;
            border-radius: 50%;
            margin-right: 0.4375rem;
          }
        }
      }
    }
  }
  .bottom {
    height: 5.25rem;
    margin: 1rem 0;
    .user-info-top-right {
      margin: 0 0.94rem;
      background: #eff8ff;
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 5.25rem;
      .button {
        width: 9.38rem;
        height: 3.75rem;
        background-size: 100% 100%;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 0.5rem;
        image {
          width: 1.75rem;
          height: 1.5rem;
          margin-bottom: 0.2rem;
        }
        .label {
          font-size: 0.94rem;
          color: $color-black;
          font-weight: 500;
        }
      }
      .line {
        width: 0.06rem;
        height: 1.25rem;
        background: $color-black;
      }
    }
  }

  .blockList {
    padding: 0rem 1rem 0;
    border-radius: 1rem;
    display: flex;
    flex-wrap: wrap;
    .block {
      width: 25%;
      .img_wrap {
        width: 1.88rem;
        height: 1.88rem;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        image {
          width: 1.88rem;
          height: 1.88rem;
        }
      }
      .text {
        font-size: 0.71rem;
        text-align: center;
        padding: 0 0.3rem;
        color: $color-black;
        margin: 0.5rem 0;
      }
    }
  }

  .top {
    width: 100%;
    // height: 3.0625rem;
    padding: 0.9375rem;
    .user-info-bottom {
      position: absolute;
      top: 5.9063rem;
      left: 0.6563rem;
      width: 22.125rem;
      height: 3.625rem;
      border-radius: 1.25rem;
      display: flex;
      justify-content: space-between;

      .zichan-chart {
        width: 2.875rem;
        height: 2.875rem;
        margin-right: 1.0938rem;
        margin-top: 0.3125rem;
      }
    }

    .user-info-right {
      position: absolute;
      top: 2.0313rem;
      right: 0.9375rem;

      font-weight: 500;
      font-size: $uni-font-size-1;
      color: #50aff0;
      display: flex;
      justify-content: center;
      align-items: center;
      .icon-arrow {
        width: 0.375rem;
        height: 0.6563rem;
      }
    }

    .user-info-left {
      display: flex;
      align-items: center;
      .user-info-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-weight: 500;
        font-size: 1rem;
        color: #161616;
        span {
          height: 0.625rem;
          margin-bottom: 0.3125rem;
          line-height: 0.625rem;
        }
      }
    }

    .headImg {
      border-radius: 50%;
      height: 2.4375rem;
      width: 2.4375rem;
      margin-right: 0.7813rem;
    }
  }
}
</style>
