<template>
  <view class="">
    <view class="navigator">
      <slot name="left"></slot>
      <view class="back" @click="goBack">
        <van-icon name="arrow" color="#333" size="1.3rem" />
      </view>
      <view class="title" style="display: flex; align-items: center; justify-content: center; width: 70%">
        <view class="title hh">{{ t('rongzi.title') }}</view>
      </view>
    </view>
    <view v-if="state == -1" class="body-box">
      <scroll-view scroll-y :style="{ height: `calc(${store.pageHeight} - 3.125rem)` }" class="container">
        <div class="bg"></div>
        <view class="money">
          <view class="tip-box">
            <div class="left">
              <view class="tip">{{ t('rongzi.tip38') }}</view>
              <view class="value"
                >{{ t('rongzi.tip39') }}<span>{{ t('rongzi.tip40') }}</span></view
              >
            </div>
            <div class="right">
              <img src="/static/image/rongzi/top.png" alt="" />
            </div>
          </view>
          <view class="card">
            <view class="tab-flex">
              <view class="tab-li">
                <img src="/static/image/rongzi/logo1.png" class="icon" />
                <div class="right">
                  <view class="desc">{{ t('rongzi.tip1') }}</view>
                  <view class="main">{{ t('rongzi.tip2') }}</view>
                </div>
              </view>
              <view class="tab-li">
                <img src="/static/image/rongzi/logo2.png" class="icon" />
                <div class="right">
                  <view class="desc">{{ t('rongzi.tip3') }}</view>
                  <view class="main">{{ t('rongzi.tip4') }}</view>
                </div>
              </view>
              <view class="tab-li">
                <img src="/static/image/rongzi/logo3.png" class="icon" />
                <div class="right">
                  <view class="desc">{{ t('rongzi.tip5') }}</view>
                  <view class="main">{{ t('rongzi.tip6') }}</view>
                </div>
              </view>
            </view>
            <view class="sq-txt">{{ t('rongzi.tip7') }}</view>
            <view class="color-gary">
              <view>{{ t('rongzi.tip8') }}</view>
              <view>{{ t('rongzi.tip9') }}</view>
              <view>{{ t('rongzi.tip10') }}</view>
            </view>

            <view class="info-box">
              <view class="title">{{ t('rongzi.tip11') }}</view>
              <view class="list">
                <view style="width: auto; color: $color-black">￥</view>
                <input v-model="params.money" style="font-size: 1.3125rem" type="number" :placeholder="t('rongzi.tip12')" />
              </view>
              <view class="xian"></view>
            </view>

            <view class="info-box">
              <view class="fs-16 list title">{{ t('rongzi.tip13') }}</view>
              <view class="xian"></view>
              <view class="list">
                <view class="list-title">{{ t('rongzi.tip15') }}</view>
                <view>
                  <input v-model="params.name" :placeholder="t('rongzi.tip14')" class="info-input" />
                </view>
              </view>
              <view class="xian"></view>
              <view class="list">
                <view class="list-title">{{ t('rongzi.tip16') }}</view>
                <view>
                  <input v-model="params.card" :placeholder="t('rongzi.tip17')" class="info-input" />
                </view>
              </view>
              <view class="xian"></view>
              <view class="list">
                <view class="list-title">{{ t('rongzi.tip18') }}</view>
                <view>
                  <input v-model="params.address" :placeholder="t('rongzi.tip19')" class="info-input" />
                </view>
              </view>
              <view class="xian"></view>
              <view class="list">
                <view class="list-title">{{ t('rongzi.tip20') }}</view>
                <view>
                  <input v-model="params.phone" :placeholder="t('rongzi.tip21')" class="info-input" />
                </view>
              </view>
              <view class="xian"></view>
              <view class="list">
                <view class="list-title">{{ t('rongzi.tip22') }}</view>
                <view>
                  <input v-model="params.mail" :placeholder="t('rongzi.tip23')" class="info-input" />
                </view>
              </view>
            </view>
            <view class="sumbit" @click="saveSumbit()">{{ t('rongzi.tip24') }}</view>
          </view>
        </view>
      </scroll-view>
    </view>
    <view v-else class="state-container">
      <image v-if="state === 0" class="state-image" src="/static/image/rongzi/state11.png" mode="widthFix" />
      <image v-if="state === 1" class="state-image1" src="/static/image/rongzi/state22.png" mode="widthFix" />
      <image v-if="state === 2" class="state-image2" src="/static/image/rongzi/state33.png" mode="widthFix" />
      <view v-if="state === 0" class="tip1" style="color: $color-black">{{ tip1 }}</view>
      <view v-if="state === 1" class="tip1 green">{{ tip1 }}</view>
      <view v-if="state === 2" class="tip1 red">{{ tip1 }}</view>
      <view class="tip2">{{ tip2 }}</view>
      <view class="but-box">
        <view class="first" @click="goWeb()">{{ t('rongzi.tip36') }}</view>
        <view v-if="state == 0" class="last" @click="switchTab('/pages/index/index')">{{ t('rongzi.tip37') }}</view>
        <view v-else-if="state == 1" class="last" @click="state = -1">{{ t('rongzi.tip25') }}</view>
        <view v-else-if="state == 2" class="last" @click="state = -1">{{ t('rongzi.tip26') }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onShow } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
import { getRongziApi, rongziApi } from '@/api/index/index'
import { switchTab, goBack } from '@/common/common'
import { ref, computed } from 'vue'
import { getCzhiurlApi } from '@/api/user'
// import bg from '@/static/image/rongzi/bg.png'
// import bg from '@/static/image/rongzi/bg.png'
import { useCounterStore } from '@/store/store'
import { closeToast, showLoadingToast, showToast } from 'vant'
const store = useCounterStore()
const { t } = useI18n()
// 页面基础配置
onShow(() => {
  getRongziList()
  getCzhiurlFn()
})

const kefu_url = ref('')

const getCzhiurlFn = async () => {
  const res = await getCzhiurlApi()
  if (res.code === 1) {
    kefu_url.value = res.data.kefu_url
  }
}

const goWeb = () => {
  window.open(kefu_url.value)
}

// 獲取融资详情
const rongziInfo = ref({})
const getRongziList = async () => {
  showLoadingToast({ forbidClick: true })
  const res = await getRongziApi()
  rongziInfo.value = res.data.data
  if (res.data?.state === 0) {
    state.value = 0
  } else if (res.data?.state === 1) {
    state.value = 1
  } else if (res.data?.state === 2) {
    state.value = 2
  } else {
    state.value = -1
  }
  closeToast()
}

const state = ref(-1)
const tip1 = computed(() => {
  const text = [t('rongzi.tip27'), t('rongzi.tip28'), t('rongzi.tip29')]
  return text[state.value]
})
const tip2 = computed(() => {
  const text2 = [t('rongzi.tip30'), t('rongzi.tip31'), t('rongzi.tip32')]
  return text2[state.value]
})

const params = ref({
  money: null,
  name: null,
  card: null,
  address: null,
  phone: null,
  mail: null,
  income: null,
  credit: null,
  qualifications: null
})

let ist = true

const saveSumbit = async () => {
  if (!ist) return
  ist = false
  if (params.value.money < 500000) {
    ist = true
    return showToast({
      icon: 'none',
      message: t('rongzi.tip33')
    })
  }
  if (params.value.money > 3000000) {
    ist = true
    return showToast({
      message: t('rongzi.tip34')
    })
  }
  if (validInfo() === true) {
    const res = await rongziApi(params.value)
    ist = true
    params.value = {
      money: null,
      name: null,
      card: null,
      address: null,
      phone: null,
      mail: null,
      income: null,
      credit: null,
      qualifications: null
    }
    if (res.code === 1) {
      state.value = 0
    }
    closeToast()
  } else {
    ist = true
  }
}

const validInfo = () => {
  if (params.value.name && params.value.card && params.value.address && params.value.phone && params.value.mail) {
    return true
  } else {
    showToast({
      message: t('rongzi.tip35')
    })
    return false
  }
}
</script>
<style scoped lang="scss">
.container {
  position: relative;
  .bg {
    height: calc(var(--vh) * 100);
    // background: url('@/static/image/rongzi/bg.png');
    background-color: #f4ffff;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 100%;
    position: fixed;
    // top: -3.13rem;
    z-index: -1;
  }
}
.uni-app--maxwidth {
  overflow-x: hidden;
}

.header {
  position: fixed;
  background: #ffffff;
  width: 100%;
  display: flex;
  height: 3.125rem;
  padding: 0.625rem;
  z-index: 9;
  display: flex;
  justify-content: space-between;

  .back {
    width: 0.5rem;
    height: 1rem;
  }

  .title {
    margin-right: 2.5rem;
    color: #000;
  }
}
.money {
  overflow: hidden;
}
.tip-box {
  overflow: hidden;
  background: $color-white;
  height: 8.13rem;
  margin: 0.94rem 1rem;
  position: relative;
  display: flex;
  border-radius: 1rem;
  z-index: 100;
  background: linear-gradient(94deg, #12719f 0.49%, #2093cb 99.51%);
  box-shadow: 0rem 0.63rem 0.94rem 0rem rgba(27, 142, 216, 0.2);
  align-items: center;
  .left {
    padding: 1.25rem 0.64rem;
  }
  .right {
    img {
      width: 8.13rem;
    }
  }
  .tip {
    color: #fff;
    text-align: center;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    font-size: 0.84rem;
  }

  .value {
    color: #fff;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 2.375rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    margin-top: 0.31rem;

    span {
      color: #fff;
      font-family: 'PingFang SC';
      font-size: 1.125rem;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
  }
}

.top-bg {
  width: 100%;
  height: 18.75rem;
}

.tab-flex {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-radius: 1.5rem;
  padding: 0.63rem 0;
}

.card {
  padding: 1.38rem 0.94rem 2rem;
  position: relative;
  z-index: 100;
}

.tab-li {
  text-align: center;
  border-radius: 0.31rem;
  align-items: center;
  width: 33%;
  padding: 0 0.31rem;
  .icon {
    width: 3.06rem;
    height: 3.06rem;
    margin: 0 auto;
  }

  .right {
    padding-left: 0.31rem;
  }

  .desc {
    color: $color-black;
    font-weight: bold;
    font-size: 0.75rem;
    text-align: center;
    margin: 0.31rem 0;
  }

  .main {
    color: $color-black;
    font-size: 0.75rem;
    text-align: center;
  }
}

.sq-txt {
  margin: 0.625rem;
  color: $color-black;

  font-size: 1.125rem;
}

.color-gary {
  font-size: 0.75rem;
  margin: 0.3125rem 0.625rem;
  uni-view {
    color: $color-black;
    white-space: pre;
    margin-top: 0.63rem;
  }
}

.info-box {
  margin: 1.25rem 0.625rem;
  // background: rgba(9, 29, 51, 0.9);
  border-radius: 0.625rem;
  padding: 0.625rem;
  .title {
    color: $color-black;
  }
}

.xian {
  border-bottom: 0.0625rem solid #36394225;
}

.list {
  line-height: 2.5rem;
  display: flex;
  align-items: center;
  margin: 0.31rem 0;
  width: 100%;
  ::v-deep .uni-input-input {
    font-size: 0.875rem;
    color: $color-black;
  }
  ::v-deep .uni-input-placeholder {
    font-size: 0.875rem;
    color: $color-gray;
  }
}
.list .list-title {
  width: 6.4375rem;
  line-height: 1.375rem;
  color: $color-black;
}

.list > view {
  font-size: 0.875rem;
}

.c-79 {
  color: $color-black;
}

.mt-10 {
  margin-top: 0.625rem;
}

input {
  font-size: 1.125rem;
  width: 100%;
  height: 1.875rem;
  line-height: 0.9375rem;
  color: $color-black;
  border: 0.05rem solid $color-white;
  border-radius: 0.38rem;
  padding: 0 0.51rem;
}

.file-box {
  width: 6.25rem;
  height: 6.25rem;
  margin-top: 0.9375rem;
  margin-bottom: 0.9375rem;
  border-radius: 0.625rem;
}

.sumbit {
  background: $color-primary;
  color: $color-white;
  font-weight: 500;
  margin: 0.625rem;
  border-radius: 0.5rem;

  line-height: 3.125rem;
  text-align: center;
}

.upload-Image {
  display: flex;
  align-items: center;
}

.ml-10 {
  margin-left: 0.625rem;
}

.state-container {
  overflow: hidden;
  text-align: center;
  overflow: hidden;

  .state-image {
    width: 11.56rem;
    margin: 6.25rem auto 1.5625rem auto;
    display: block;
  }

  .state-image1 {
    width: 11.56rem;
    margin: 6.25rem auto 1.5625rem auto;
    display: block;
  }

  .state-image2 {
    width: 11.56rem;
    margin: 6.25rem auto 1.5625rem auto;
    display: block;
  }

  .tip1 {
    font-size: 1rem;
    font-weight: 600;
    color: #000;
  }

  .tip2 {
    font-size: 0.8rem;
    font-weight: 400;
    color: #a6a6a6;
    padding: 0.5rem 0 2.25rem 0;
    margin: 0 1rem;
  }

  .but-box {
    display: flex;
    justify-content: space-around;

    view {
      width: 10.5rem;
      height: 2.9375rem;
      line-height: 2.9375rem;
      border-radius: 0.625rem;
    }

    .first {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      background: $color-primary;
      font-weight: 500;
      border-radius: 0.5rem;
      color: $color-white;
    }

    .last {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      border-radius: 0.5rem;
      background: transparent;
      border: 0.05rem solid $color-primary;
      color: $color-primary;
    }
  }
}

.navigator {
  height: 3.13rem;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  .back {
    position: absolute;
    left: 0;
    height: 100%;
    padding-left: 1.0625rem;
    display: flex;
    align-items: center;
    .van-icon {
      transform: rotate(180deg);
    }
  }
  .title {
    font-size: 0.9375rem;
    color: $color-black;
    font-weight: 500;
    text-align: center;
  }
}
</style>
